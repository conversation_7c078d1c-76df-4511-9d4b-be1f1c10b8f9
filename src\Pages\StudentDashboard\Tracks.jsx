import React from 'react'
import {
  FiCode,        // Web Development
  FiSmartphone,  // Mobile App Development
  FiDatabase,    // Data Science
  FiShield,      // Cybersecurity
  FiCloud,       // Cloud Computing
  FiSettings,    // DevOps
  FiCpu,         // Embedded & IoT
  FiLayers,      // Blockchain
  FiCheckSquare, // QA & Testing
  FiPenTool,     // UI/UX Design
} from "react-icons/fi";

import { GiBrain, GiGamepad } from "react-icons/gi";
function Tracks() {
  export const tracks = [
  {
    id: 1,
    title: "Web Development",
    icon: <FiCode />,
    roadmap: [
      "HTML Basics",
      "CSS Styling",
      "JavaScript Fundamentals",
      "Responsive Design",
      "Git & GitHub",
      "Frontend Frameworks (React/Vue/Angular)",
      "State Management",
      "APIs & REST",
      "GraphQL",
      "Backend Basics (Node.js/Express)",
      "Databases (SQL/NoSQL)",
      "Authentication & Security",
      "Testing (Unit/E2E)",
      "Deployment (CI/CD, Docker, Vercel, Netlify)",
      "Full Stack Projects & Portfolio",
    ],
  },
  {
    id: 2,
    title: "Mobile App Development",
    icon: <FiSmartphone />,
    roadmap: [
      "Java/Kotlin Basics (Android)",
      "Swift/Objective-C (iOS)",
      "Cross-platform (React Native, Flutter)",
      "UI Components & Styling",
      "State Management",
      "APIs Integration",
      "Authentication",
      "Local Storage & Databases",
      "Push Notifications",
      "App Security",
      "App Deployment (Google Play / App Store)",
      "CI/CD for Mobile",
      "Performance Optimization",
      "Testing (Unit/UI/Integration)",
      "Advanced Features (AR/VR, ML Integration)",
    ],
  },
  {
    id: 3,
    title: "Data Science",
    icon: <FiDatabase />,
    roadmap: [
      "Math & Statistics Fundamentals",
      "Python Basics",
      "Data Manipulation (Pandas, Numpy)",
      "Data Visualization (Matplotlib, Seaborn)",
      "Exploratory Data Analysis",
      "SQL & Databases",
      "Machine Learning Basics",
      "Supervised Learning",
      "Unsupervised Learning",
      "Deep Learning Basics",
      "NLP Basics",
      "Big Data Tools (Hadoop, Spark)",
      "Model Deployment",
      "MLOps Basics",
      "Capstone Projects",
    ],
  },
  {
    id: 4,
    title: "Cybersecurity",
    icon: <FiShield />,
    roadmap: [
      "Networking Fundamentals",
      "Operating Systems Security",
      "Linux Basics",
      "Cryptography",
      "Web Security",
      "Vulnerability Assessment",
      "Ethical Hacking",
      "Penetration Testing",
      "Security Tools (Wireshark, Metasploit)",
      "Malware Analysis",
      "Cloud Security",
      "Incident Response",
      "Digital Forensics",
      "Security Compliance & Laws",
      "Advanced Red/Blue Teaming",
    ],
  },
  {
    id: 5,
    title: "Cloud Computing",
    icon: <FiCloud />,
    roadmap: [
      "Cloud Fundamentals",
      "AWS Basics",
      "Azure Basics",
      "GCP Basics",
      "Virtualization",
      "Containers (Docker)",
      "Kubernetes",
      "Serverless Computing",
      "Networking in Cloud",
      "Cloud Databases",
      "Cloud Security",
      "CI/CD in Cloud",
      "Terraform & IaC",
      "Monitoring & Logging",
      "Advanced Cloud Architectures",
    ],
  },
  {
    id: 6,
    title: "DevOps",
    icon: <FiSettings />,
    roadmap: [
      "Linux Basics",
      "Shell Scripting",
      "Git & Version Control",
      "CI/CD Pipelines",
      "Docker",
      "Kubernetes",
      "Infrastructure as Code (Terraform)",
      "Monitoring Tools (Prometheus, Grafana)",
      "Logging & Tracing",
      "Cloud Providers",
      "Security in DevOps",
      "Automation Tools (Ansible, Puppet, Chef)",
      "Performance Testing",
      "DevSecOps",
      "Scaling & High Availability",
    ],
  },
  {
    id: 7,
    title: "Embedded Systems & IoT",
    icon: <FiCpu />,
    roadmap: [
      "C/C++ Basics",
      "Microcontrollers (Arduino, STM32)",
      "Embedded C Programming",
      "RTOS Basics",
      "IoT Fundamentals",
      "IoT Communication Protocols (MQTT, CoAP)",
      "Sensors & Actuators",
      "Wireless Communication (BLE, LoRa, Zigbee)",
      "Cloud IoT Platforms",
      "Edge Computing",
      "Security in IoT",
      "Firmware Development",
      "Testing Embedded Systems",
      "IoT Applications",
      "Smart Devices & Automation",
    ],
  },
  {
    id: 8,
    title: "Blockchain",
    icon: <FiLayers />,
    roadmap: [
      "Blockchain Fundamentals",
      "Cryptography Basics",
      "Ethereum Basics",
      "Smart Contracts",
      "Solidity Programming",
      "DApps Development",
      "Consensus Algorithms",
      "DeFi Basics",
      "NFTs",
      "Layer2 Solutions",
      "Blockchain Security",
      "Interoperability",
      "Scalability Solutions",
      "Testing Smart Contracts",
      "Blockchain Projects",
    ],
  },
  {
    id: 9,
    title: "QA & Testing",
    icon: <FiCheckSquare />,
    roadmap: [
      "Testing Fundamentals",
      "Manual Testing",
      "Agile & Scrum Basics",
      "Unit Testing",
      "Integration Testing",
      "E2E Testing",
      "Automation Testing (Selenium, Cypress)",
      "Performance Testing",
      "Security Testing",
      "Test Management Tools (Jira, TestRail)",
      "API Testing (Postman)",
      "Mobile App Testing",
      "CI/CD Testing",
      "Bug Reporting & Tracking",
      "QA Leadership & Strategy",
    ],
  },
  {
    id: 10,
    title: "UI/UX Design",
    icon: <FiPenTool />,
    roadmap: [
      "Design Fundamentals",
      "Color Theory & Typography",
      "Wireframing",
      "Prototyping (Figma, Adobe XD)",
      "User Research",
      "User Personas",
      "Accessibility Standards",
      "Design Systems",
      "Motion Design",
      "UI Patterns",
      "Responsive Design",
      "Usability Testing",
      "Collaboration with Developers",
      "DesignOps",
      "Portfolio & Case Studies",
    ],
  },
  {
    id: 11,
    title: "Machine Learning & AI",
    icon: <GiBrain />,
    roadmap: [
      "Math & Linear Algebra",
      "Python for ML",
      "Data Preprocessing",
      "Supervised ML",
      "Unsupervised ML",
      "Neural Networks",
      "Deep Learning",
      "CNNs",
      "RNNs",
      "Transformers",
      "Reinforcement Learning",
      "NLP Advanced",
      "MLOps & Deployment",
      "AI Ethics",
      "Research & Advanced Projects",
    ],
  },
  {
    id: 12,
    title: "Game Development",
    icon: <GiGamepad />,
    roadmap: [
      "Game Design Principles",
      "Programming Basics (C#/C++)",
      "Game Engines (Unity/Unreal)",
      "2D Game Development",
      "3D Game Development",
      "Physics in Games",
      "AI in Games",
      "Multiplayer Systems",
      "Animation & Rigging",
      "Audio in Games",
      "Shaders & Graphics",
      "Optimization Techniques",
      "Testing Games",
      "Publishing Games",
      "Monetization & Marketing",
    ],
  },
];
  return (
    <div>
      
    </div>
  )
}

export default Tracks
