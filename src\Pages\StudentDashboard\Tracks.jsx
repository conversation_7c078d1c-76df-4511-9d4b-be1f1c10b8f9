import React, { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";
import {
  FiCode, // Web Development
  FiSmartphone, // Mobile App Development
  FiDatabase, // Data Science
  FiShield, // Cybersecurity
  FiCloud, // Cloud Computing
  FiSettings, // DevOps
  FiCpu, // Embedded & IoT
  FiLayers, // Blockchain
  FiCheckSquare, // QA & Testing
  FiPenTool, // UI/UX Design
  FiArrowLeft, // Back button
  FiStar, // Star icon for milestones
} from "react-icons/fi";

import { GiBrain, GiGamepad } from "react-icons/gi";

const tracks = [
  {
    id: 1,
    title: "Web Development",
    icon: <FiCode />,
    color: "from-blue-500 to-cyan-500",
    glowColor: "shadow-blue-500/50",
    roadmap: [
      "HTML Basics",
      "CSS Styling",
      "JavaScript Fundamentals",
      "Responsive Design",
      "Git & GitHub",
      "Frontend Frameworks (React/Vue/Angular)",
      "State Management",
      "APIs & REST",
      "GraphQL",
      "Backend Basics (Node.js/Express)",
      "Databases (SQL/NoSQL)",
      "Authentication & Security",
      "Testing (Unit/E2E)",
      "Deployment (CI/CD, Docker, Vercel, Netlify)",
      "Full Stack Projects & Portfolio",
    ],
  },
  {
    id: 2,
    title: "Mobile App Development",
    icon: <FiSmartphone />,
    color: "from-purple-500 to-pink-500",
    glowColor: "shadow-purple-500/50",
    roadmap: [
      "Java/Kotlin Basics (Android)",
      "Swift/Objective-C (iOS)",
      "Cross-platform (React Native, Flutter)",
      "UI Components & Styling",
      "State Management",
      "APIs Integration",
      "Authentication",
      "Local Storage & Databases",
      "Push Notifications",
      "App Security",
      "App Deployment (Google Play / App Store)",
      "CI/CD for Mobile",
      "Performance Optimization",
      "Testing (Unit/UI/Integration)",
      "Advanced Features (AR/VR, ML Integration)",
    ],
  },
  {
    id: 3,
    title: "Data Science",
    icon: <FiDatabase />,
    color: "from-green-500 to-emerald-500",
    glowColor: "shadow-green-500/50",
    roadmap: [
      "Math & Statistics Fundamentals",
      "Python Basics",
      "Data Manipulation (Pandas, Numpy)",
      "Data Visualization (Matplotlib, Seaborn)",
      "Exploratory Data Analysis",
      "SQL & Databases",
      "Machine Learning Basics",
      "Supervised Learning",
      "Unsupervised Learning",
      "Deep Learning Basics",
      "NLP Basics",
      "Big Data Tools (Hadoop, Spark)",
      "Model Deployment",
      "MLOps Basics",
      "Capstone Projects",
    ],
  },
  {
    id: 4,
    title: "Cybersecurity",
    icon: <FiShield />,
    color: "from-red-500 to-orange-500",
    glowColor: "shadow-red-500/50",
    roadmap: [
      "Networking Fundamentals",
      "Operating Systems Security",
      "Linux Basics",
      "Cryptography",
      "Web Security",
      "Vulnerability Assessment",
      "Ethical Hacking",
      "Penetration Testing",
      "Security Tools (Wireshark, Metasploit)",
      "Malware Analysis",
      "Cloud Security",
      "Incident Response",
      "Digital Forensics",
      "Security Compliance & Laws",
      "Advanced Red/Blue Teaming",
    ],
  },
  {
    id: 5,
    title: "Cloud Computing",
    icon: <FiCloud />,
    color: "from-sky-500 to-blue-500",
    glowColor: "shadow-sky-500/50",
    roadmap: [
      "Cloud Fundamentals",
      "AWS Basics",
      "Azure Basics",
      "GCP Basics",
      "Virtualization",
      "Containers (Docker)",
      "Kubernetes",
      "Serverless Computing",
      "Networking in Cloud",
      "Cloud Databases",
      "Cloud Security",
      "CI/CD in Cloud",
      "Terraform & IaC",
      "Monitoring & Logging",
      "Advanced Cloud Architectures",
    ],
  },
  {
    id: 6,
    title: "DevOps",
    icon: <FiSettings />,
    color: "from-indigo-500 to-purple-500",
    glowColor: "shadow-indigo-500/50",
    roadmap: [
      "Linux Basics",
      "Shell Scripting",
      "Git & Version Control",
      "CI/CD Pipelines",
      "Docker",
      "Kubernetes",
      "Infrastructure as Code (Terraform)",
      "Monitoring Tools (Prometheus, Grafana)",
      "Logging & Tracing",
      "Cloud Providers",
      "Security in DevOps",
      "Automation Tools (Ansible, Puppet, Chef)",
      "Performance Testing",
      "DevSecOps",
      "Scaling & High Availability",
    ],
  },
  {
    id: 7,
    title: "Embedded Systems & IoT",
    icon: <FiCpu />,
    color: "from-amber-500 to-orange-500",
    glowColor: "shadow-amber-500/50",
    roadmap: [
      "C/C++ Basics",
      "Microcontrollers (Arduino, STM32)",
      "Embedded C Programming",
      "RTOS Basics",
      "IoT Fundamentals",
      "IoT Communication Protocols (MQTT, CoAP)",
      "Sensors & Actuators",
      "Wireless Communication (BLE, LoRa, Zigbee)",
      "Cloud IoT Platforms",
      "Edge Computing",
      "Security in IoT",
      "Firmware Development",
      "Testing Embedded Systems",
      "IoT Applications",
      "Smart Devices & Automation",
    ],
  },
  {
    id: 8,
    title: "Blockchain Development",
    icon: <FiLayers />,
    color: "from-yellow-500 to-amber-500",
    glowColor: "shadow-yellow-500/50",
    roadmap: [
      "Blockchain Fundamentals",
      "Cryptography Basics",
      "Ethereum Basics",
      "Smart Contracts",
      "Solidity Programming",
      "DApps Development",
      "Consensus Algorithms",
      "DeFi Basics",
      "NFTs",
      "Layer2 Solutions",
      "Blockchain Security",
      "Interoperability",
      "Scalability Solutions",
      "Testing Smart Contracts",
      "Blockchain Projects",
    ],
  },
  {
    id: 9,
    title: "Software Testing & QA",
    icon: <FiCheckSquare />,
    color: "from-teal-500 to-cyan-500",
    glowColor: "shadow-teal-500/50",
    roadmap: [
      "Testing Fundamentals",
      "Manual Testing",
      "Agile & Scrum Basics",
      "Unit Testing",
      "Integration Testing",
      "E2E Testing",
      "Automation Testing (Selenium, Cypress)",
      "Performance Testing",
      "Security Testing",
      "Test Management Tools (Jira, TestRail)",
      "API Testing (Postman)",
      "Mobile App Testing",
      "CI/CD Testing",
      "Bug Reporting & Tracking",
      "QA Leadership & Strategy",
    ],
  },
  {
    id: 10,
    title: "UI/UX Design",
    icon: <FiPenTool />,
    color: "from-pink-500 to-rose-500",
    glowColor: "shadow-pink-500/50",
    roadmap: [
      "Design Fundamentals",
      "Color Theory & Typography",
      "Wireframing",
      "Prototyping (Figma, Adobe XD)",
      "User Research",
      "User Personas",
      "Accessibility Standards",
      "Design Systems",
      "Motion Design",
      "UI Patterns",
      "Responsive Design",
      "Usability Testing",
      "Collaboration with Developers",
      "DesignOps",
      "Portfolio & Case Studies",
    ],
  },
  {
    id: 11,
    title: "Machine Learning & AI",
    icon: <GiBrain />,
    color: "from-violet-500 to-purple-500",
    glowColor: "shadow-violet-500/50",
    roadmap: [
      "Math & Linear Algebra",
      "Python for ML",
      "Data Preprocessing",
      "Supervised ML",
      "Unsupervised ML",
      "Neural Networks",
      "Deep Learning",
      "CNNs",
      "RNNs",
      "Transformers",
      "Reinforcement Learning",
      "NLP Advanced",
      "MLOps & Deployment",
      "AI Ethics",
      "Research & Advanced Projects",
    ],
  },
  {
    id: 12,
    title: "Game Development",
    icon: <GiGamepad />,
    color: "from-emerald-500 to-green-500",
    glowColor: "shadow-emerald-500/50",
    roadmap: [
      "Game Design Principles",
      "Programming Basics (C#/C++)",
      "Game Engines (Unity/Unreal)",
      "2D Game Development",
      "3D Game Development",
      "Physics in Games",
      "AI in Games",
      "Multiplayer Systems",
      "Animation & Rigging",
      "Audio in Games",
      "Shaders & Graphics",
      "Optimization Techniques",
      "Testing Games",
      "Publishing Games",
      "Monetization & Marketing",
    ],
  },
];

function Tracks() {
  const [selectedTrack, setSelectedTrack] = useState(null);
  const [selectedMilestone, setSelectedMilestone] = useState(null);

  const handleTrackSelect = (track) => {
    setSelectedTrack(track);
    setSelectedMilestone(null);
  };

  const handleBackToTracks = () => {
    setSelectedTrack(null);
    setSelectedMilestone(null);
  };

  const handleMilestoneClick = (index) => {
    setSelectedMilestone(index);
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-blue-900 to-slate-800 p-6">
      <AnimatePresence mode="wait">
        {!selectedTrack ? (
          <motion.div
            key="tracks-grid"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.5 }}
            className="max-w-7xl mx-auto"
          >
            {/* Header */}
            <motion.div
              initial={{ opacity: 0, y: -30 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.2 }}
              className="text-center mb-12"
            >
              <h1 className="text-5xl font-bold mb-4 cosmic-text">
                Choose Your Career Track
              </h1>
              <p className="text-xl text-gray-300 mb-2">
                Explore your roadmap and start your journey 🚀
              </p>
              <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-500 mx-auto rounded-full"></div>
            </motion.div>

            {/* Tracks Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
              {tracks.map((track, index) => (
                <motion.div
                  key={track.id}
                  initial={{ opacity: 0, y: 50, scale: 0.9 }}
                  animate={{ opacity: 1, y: 0, scale: 1 }}
                  transition={{
                    duration: 0.5,
                    delay: index * 0.1,
                    type: "spring",
                    stiffness: 100,
                  }}
                  whileHover={{
                    scale: 1.05,
                    y: -10,
                    transition: { duration: 0.2 },
                  }}
                  whileTap={{ scale: 0.95 }}
                  onClick={() => handleTrackSelect(track)}
                  className="group cursor-pointer"
                >
                  <div
                    className={`
                    relative overflow-hidden rounded-2xl p-6 h-48
                    bg-gradient-to-br ${track.color}
                    shadow-2xl ${track.glowColor}
                    border border-white/20
                    backdrop-blur-sm
                    transition-all duration-300
                    hover:shadow-3xl hover:border-white/40
                  `}
                  >
                    {/* Cosmic Background Effects */}
                    <div className="absolute inset-0 opacity-20">
                      <div className="absolute top-2 right-2 w-2 h-2 bg-white rounded-full cosmic-sparkle"></div>
                      <div
                        className="absolute bottom-4 left-4 w-1 h-1 bg-white rounded-full cosmic-sparkle"
                        style={{ animationDelay: "1s" }}
                      ></div>
                      <div
                        className="absolute top-1/2 left-1/2 w-1.5 h-1.5 bg-white rounded-full cosmic-sparkle"
                        style={{ animationDelay: "2s" }}
                      ></div>
                    </div>

                    {/* Content */}
                    <div className="relative z-10 h-full flex flex-col justify-between">
                      <div className="flex items-center justify-center mb-4">
                        <motion.div
                          whileHover={{ rotate: 360 }}
                          transition={{ duration: 0.6 }}
                          className="text-5xl text-white cosmic-glow"
                        >
                          {track.icon}
                        </motion.div>
                      </div>

                      <div className="text-center">
                        <h3 className="text-xl font-bold text-white mb-2 neon-glow">
                          {track.title}
                        </h3>
                        <div className="text-sm text-white/80">
                          {track.roadmap.length} milestones
                        </div>
                      </div>
                    </div>

                    {/* Hover Effect Overlay */}
                    <motion.div
                      initial={{ opacity: 0 }}
                      whileHover={{ opacity: 1 }}
                      className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl"
                    />
                  </div>
                </motion.div>
              ))}
            </div>
          </motion.div>
        ) : (
          <RoadmapView
            track={selectedTrack}
            onBack={handleBackToTracks}
            selectedMilestone={selectedMilestone}
            onMilestoneClick={handleMilestoneClick}
          />
        )}
      </AnimatePresence>
    </div>
  );
}

// Roadmap View Component
const RoadmapView = ({
  track,
  onBack,
  selectedMilestone,
  onMilestoneClick,
}) => {
  return (
    <motion.div
      key="roadmap-view"
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      exit={{ opacity: 0, scale: 0.9 }}
      transition={{ duration: 0.5 }}
      className="max-w-6xl mx-auto"
    >
      {/* Header */}
      <motion.div
        initial={{ opacity: 0, y: -30 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, delay: 0.2 }}
        className="flex items-center justify-between mb-8"
      >
        <motion.button
          onClick={onBack}
          whileHover={{ scale: 1.05, x: -5 }}
          whileTap={{ scale: 0.95 }}
          className="flex items-center space-x-2 px-6 py-3 bg-white/10 backdrop-blur-sm rounded-xl border border-white/20 text-white hover:bg-white/20 transition-all duration-300"
        >
          <FiArrowLeft className="text-xl" />
          <span>Back to Tracks</span>
        </motion.button>
        <div className="text-center">
          <h1 className="text-4xl font-bold cosmic-text mb-2">
            {track.title} Roadmap
          </h1>
          <p className="text-gray-300">Your journey to mastery</p>
        </div>
        <div className="w-32"></div> {/* Spacer for centering */}
      </motion.div>

      {/* Roadmap Flowchart */}
      <div className="relative">
        {/* Background Grid */}
        <div className="absolute inset-0 opacity-10">
          <div className="grid grid-cols-12 gap-4 h-full">
            {Array.from({ length: 48 }).map((_, i) => (
              <div key={i} className="border border-white/20"></div>
            ))}
          </div>
        </div>

        {/* Milestones */}
        <div className="relative z-10 space-y-6">
          {track.roadmap.map((milestone, index) => (
            <motion.div
              key={index}
              initial={{ opacity: 0, x: index % 2 === 0 ? -50 : 50 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{
                duration: 0.5,
                delay: index * 0.1,
                type: "spring",
                stiffness: 100,
              }}
              className={`flex items-center ${
                index % 2 === 0 ? "justify-start" : "justify-end"
              }`}
            >
              <motion.div
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                onClick={() => onMilestoneClick(index)}
                className={`
                  relative cursor-pointer group
                  ${index % 2 === 0 ? "mr-8" : "ml-8"}
                  ${selectedMilestone === index ? "z-20" : "z-10"}
                `}
              >
                {/* Milestone Card */}
                <div
                  className={`
                  relative p-6 rounded-2xl max-w-md
                  ${
                    selectedMilestone === index
                      ? `bg-gradient-to-br ${track.color} shadow-2xl ${track.glowColor} scale-110`
                      : "bg-white/10 backdrop-blur-sm border border-white/20 hover:bg-white/20"
                  }
                  transition-all duration-300
                `}
                >
                  {/* Milestone Number */}
                  <div
                    className={`
                    absolute -left-4 top-1/2 transform -translate-y-1/2
                    w-8 h-8 rounded-full flex items-center justify-center
                    ${
                      selectedMilestone === index
                        ? "bg-white text-gray-900"
                        : `bg-gradient-to-br ${track.color}`
                    }
                    text-sm font-bold
                  `}
                  >
                    {index + 1}
                  </div>

                  {/* Content */}
                  <div className="ml-4">
                    <h3
                      className={`
                      text-lg font-semibold mb-2
                      ${
                        selectedMilestone === index
                          ? "text-white neon-glow"
                          : "text-white"
                      }
                    `}
                    >
                      {milestone}
                    </h3>

                    {selectedMilestone === index && (
                      <motion.div
                        initial={{ opacity: 0, height: 0 }}
                        animate={{ opacity: 1, height: "auto" }}
                        className="text-white/90 text-sm"
                      >
                        <p>Click to explore this milestone in detail</p>
                      </motion.div>
                    )}
                  </div>

                  {/* Star Icon */}
                  <motion.div
                    whileHover={{ rotate: 180 }}
                    className={`
                      absolute -right-2 -top-2 text-2xl
                      ${
                        selectedMilestone === index
                          ? "text-yellow-300 cosmic-glow"
                          : "text-white/60"
                      }
                    `}
                  >
                    <FiStar />
                  </motion.div>

                  {/* Cosmic Effects */}
                  {selectedMilestone === index && (
                    <div className="absolute inset-0 opacity-30">
                      <div className="absolute top-1 right-1 w-1 h-1 bg-white rounded-full cosmic-sparkle"></div>
                      <div
                        className="absolute bottom-2 left-2 w-0.5 h-0.5 bg-white rounded-full cosmic-sparkle"
                        style={{ animationDelay: "0.5s" }}
                      ></div>
                    </div>
                  )}
                </div>

                {/* Connection Line */}
                {index < track.roadmap.length - 1 && (
                  <motion.div
                    initial={{ scaleY: 0 }}
                    animate={{ scaleY: 1 }}
                    transition={{ duration: 0.5, delay: (index + 1) * 0.1 }}
                    className={`
                      absolute top-full left-1/2 transform -translate-x-1/2
                      w-0.5 h-6 bg-gradient-to-b ${track.color}
                      origin-top
                    `}
                  />
                )}
              </motion.div>
            </motion.div>
          ))}
        </div>
      </div>
    </motion.div>
  );
};

export default Tracks;
